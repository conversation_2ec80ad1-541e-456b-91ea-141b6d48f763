import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { Box, CircularProgress, Typography, Paper } from '@mui/material';
import { Lock } from '@mui/icons-material';

const ProtectedRoute = ({ 
  children, 
  requiredRole = null, 
  requiredPermission = null,
  allowedRoles = null 
}) => {
  const { user, loading } = useAuth();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '50vh',
          flexDirection: 'column'
        }}
      >
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ mt: 2 }}>
          Loading...
        </Typography>
      </Box>
    );
  }

  // Redirect to login if not authenticated
  if (!user) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Check if user has required role
  if (requiredRole && user.role !== requiredRole) {
    return (
      <Box sx={{ p: 3 }}>
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Lock sx={{ fontSize: 60, color: 'error.main', mb: 2 }} />
          <Typography variant="h5" gutterBottom>
            Access Denied
          </Typography>
          <Typography variant="body1" color="text.secondary">
            You don't have permission to access this page.
            Required role: {requiredRole}
          </Typography>
        </Paper>
      </Box>
    );
  }

  // Check if user role is in allowed roles list
  if (allowedRoles && !allowedRoles.includes(user.role)) {
    return (
      <Box sx={{ p: 3 }}>
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Lock sx={{ fontSize: 60, color: 'error.main', mb: 2 }} />
          <Typography variant="h5" gutterBottom>
            Access Denied
          </Typography>
          <Typography variant="body1" color="text.secondary">
            You don't have permission to access this page.
            Allowed roles: {allowedRoles.join(', ')}
          </Typography>
        </Paper>
      </Box>
    );
  }

  // Check if user has required permission
  if (requiredPermission) {
    const hasPermission = user.role === 'admin' || 
                         (user.permissions && user.permissions.includes(requiredPermission));
    
    if (!hasPermission) {
      return (
        <Box sx={{ p: 3 }}>
          <Paper sx={{ p: 4, textAlign: 'center' }}>
            <Lock sx={{ fontSize: 60, color: 'error.main', mb: 2 }} />
            <Typography variant="h5" gutterBottom>
              Access Denied
            </Typography>
            <Typography variant="body1" color="text.secondary">
              You don't have permission to access this feature.
              Required permission: {requiredPermission}
            </Typography>
          </Paper>
        </Box>
      );
    }
  }

  // If all checks pass, render the protected component
  return children;
};

// Higher-order component for easier usage
export const withRoleProtection = (Component, options = {}) => {
  return (props) => (
    <ProtectedRoute {...options}>
      <Component {...props} />
    </ProtectedRoute>
  );
};

// Specific role-based route components for common use cases
export const AdminRoute = ({ children }) => (
  <ProtectedRoute requiredRole="admin">
    {children}
  </ProtectedRoute>
);

export const StaffRoute = ({ children }) => (
  <ProtectedRoute allowedRoles={['admin', 'staff']}>
    {children}
  </ProtectedRoute>
);



// Permission-based route components
export const AppointmentsRoute = ({ children }) => (
  <ProtectedRoute requiredPermission="appointments">
    {children}
  </ProtectedRoute>
);

export const CustomersRoute = ({ children }) => (
  <ProtectedRoute requiredPermission="customers">
    {children}
  </ProtectedRoute>
);

export const ServicesRoute = ({ children }) => (
  <ProtectedRoute requiredPermission="services">
    {children}
  </ProtectedRoute>
);

export const ReportsRoute = ({ children }) => (
  <ProtectedRoute allowedRoles={['admin']}>
    {children}
  </ProtectedRoute>
);

export const StaffManagementRoute = ({ children }) => (
  <ProtectedRoute requiredRole="admin">
    {children}
  </ProtectedRoute>
);

export default ProtectedRoute;
