import React, { createContext, useContext, useState, useEffect } from 'react';

const BillingContext = createContext();

export const useBilling = () => {
  const context = useContext(BillingContext);
  if (!context) {
    throw new Error('useBilling must be used within a BillingProvider');
  }
  return context;
};

export const BillingProvider = ({ children }) => {
  const [invoices, setInvoices] = useState([
    {
      id: 'INV-2024-001',
      customerId: 1,
      customerName: '<PERSON>',
      customerEmail: '<EMAIL>',
      customerPhone: '(*************',
      date: '2024-07-17',
      dueDate: '2024-07-24',
      services: [
        {
          id: 1,
          name: 'Hair Cut & Style',
          price: 85,
          stylist: '<PERSON>',
          duration: 60
        },
        {
          id: 2,
          name: 'Hair Color',
          price: 120,
          stylist: '<PERSON>',
          duration: 90
        }
      ],
      subtotal: 205,
      discountType: 'percentage',
      discountValue: 10,
      discountAmount: 20.5,
      taxRate: 8.5,
      taxAmount: 15.68,
      total: 200.18,
      status: 'paid',
      paymentMethod: 'card',
      paymentStatus: 'completed',
      paymentDate: '2024-07-17',
      transactionId: 'txn_1234567890',
      notes: 'Regular customer - 10% loyalty discount applied',
      createdAt: '2024-07-17T10:30:00Z',
      updatedAt: '2024-07-17T10:35:00Z'
    },
    {
      id: 'INV-2024-002',
      customerId: 2,
      customerName: 'Mike Davis',
      customerEmail: '<EMAIL>',
      customerPhone: '(*************',
      date: '2024-07-17',
      dueDate: '2024-07-24',
      services: [
        {
          id: 3,
          name: 'Beard Trim',
          price: 35,
          stylist: 'John Smith',
          duration: 30
        }
      ],
      subtotal: 35,
      discountType: null,
      discountValue: 0,
      discountAmount: 0,
      taxRate: 8.5,
      taxAmount: 2.98,
      total: 37.98,
      status: 'pending',
      paymentMethod: null,
      paymentStatus: 'pending',
      paymentDate: null,
      transactionId: null,
      notes: '',
      createdAt: '2024-07-17T14:15:00Z',
      updatedAt: '2024-07-17T14:15:00Z'
    }
  ]);

  const [payments, setPayments] = useState([
    {
      id: 'PAY-001',
      invoiceId: 'INV-2024-001',
      amount: 200.18,
      method: 'card',
      status: 'completed',
      transactionId: 'txn_1234567890',
      gateway: 'stripe',
      date: '2024-07-17T10:35:00Z',
      cardLast4: '4242',
      cardBrand: 'visa'
    }
  ]);

  const [discounts, setDiscounts] = useState([
    {
      id: 1,
      code: 'WELCOME10',
      name: 'Welcome Discount',
      type: 'percentage',
      value: 10,
      minAmount: 50,
      maxDiscount: 25,
      validFrom: '2024-01-01',
      validTo: '2024-12-31',
      usageLimit: 100,
      usedCount: 15,
      status: 'active',
      description: 'Welcome discount for new customers'
    },
    {
      id: 2,
      code: 'SUMMER20',
      name: 'Summer Special',
      type: 'percentage',
      value: 20,
      minAmount: 100,
      maxDiscount: 50,
      validFrom: '2024-06-01',
      validTo: '2024-08-31',
      usageLimit: 50,
      usedCount: 8,
      status: 'active',
      description: 'Summer season discount'
    },
    {
      id: 3,
      code: 'FLAT15',
      name: 'Flat Discount',
      type: 'fixed',
      value: 15,
      minAmount: 75,
      maxDiscount: 15,
      validFrom: '2024-07-01',
      validTo: '2024-07-31',
      usageLimit: 25,
      usedCount: 12,
      status: 'active',
      description: 'Fixed amount discount'
    },
    {
      id: 4,
      code: 'EXPIRED10',
      name: 'Expired 10% Off',
      type: 'percentage',
      value: 10,
      minAmount: 0,
      maxDiscount: 50,
      validFrom: '2024-06-01',
      validTo: '2024-06-30',
      usageLimit: 100,
      usedCount: 45,
      status: 'active',
      description: 'Expired discount for testing'
    },
    {
      id: 5,
      code: 'EXPIRING5',
      name: 'Expiring Soon 5% Off',
      type: 'percentage',
      value: 5,
      minAmount: 0,
      maxDiscount: 25,
      validFrom: '2024-07-01',
      validTo: '2024-07-20',
      usageLimit: 50,
      usedCount: 10,
      status: 'active',
      description: 'Discount expiring soon for testing'
    }
  ]);

  const [transactions, setTransactions] = useState([
    {
      id: 'TXN-001',
      invoiceId: 'INV-2024-001',
      type: 'payment',
      amount: 200.18,
      method: 'card',
      status: 'completed',
      date: '2024-07-17T10:35:00Z',
      description: 'Payment for services'
    },
    {
      id: 'TXN-002',
      invoiceId: 'INV-2024-001',
      type: 'refund',
      amount: -20.00,
      method: 'card',
      status: 'completed',
      date: '2024-07-17T16:20:00Z',
      description: 'Partial refund - service adjustment'
    }
  ]);

  // Function to check and update expired discounts
  const checkExpiredDiscounts = () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Set to start of day for accurate comparison

    setDiscounts(prev => prev.map(discount => {
      const validToDate = new Date(discount.validTo);
      validToDate.setHours(23, 59, 59, 999); // Set to end of day

      // If discount is expired and still active, mark as inactive
      if (validToDate < today && discount.status === 'active') {
        return { ...discount, status: 'expired' };
      }

      return discount;
    }));
  };

  // Load data from localStorage on mount
  useEffect(() => {
    const savedInvoices = localStorage.getItem('salon_billing_invoices');
    const savedPayments = localStorage.getItem('salon_billing_payments');
    const savedDiscounts = localStorage.getItem('salon_billing_discounts');
    const savedTransactions = localStorage.getItem('salon_billing_transactions');

    if (savedInvoices) setInvoices(JSON.parse(savedInvoices));
    if (savedPayments) setPayments(JSON.parse(savedPayments));
    if (savedDiscounts) {
      const parsedDiscounts = JSON.parse(savedDiscounts);
      setDiscounts(parsedDiscounts);
      // Check for expired discounts after loading
      setTimeout(() => checkExpiredDiscounts(), 100);
    }
    if (savedTransactions) setTransactions(JSON.parse(savedTransactions));
  }, []);

  // Check for expired discounts periodically (every hour)
  useEffect(() => {
    const interval = setInterval(() => {
      checkExpiredDiscounts();
    }, 60 * 60 * 1000); // Check every hour

    return () => clearInterval(interval);
  }, []);

  // Check for expired discounts when component mounts
  useEffect(() => {
    checkExpiredDiscounts();
  }, []);

  // Save to localStorage whenever data changes
  useEffect(() => {
    localStorage.setItem('salon_billing_invoices', JSON.stringify(invoices));
  }, [invoices]);

  useEffect(() => {
    localStorage.setItem('salon_billing_payments', JSON.stringify(payments));
  }, [payments]);

  useEffect(() => {
    localStorage.setItem('salon_billing_discounts', JSON.stringify(discounts));
  }, [discounts]);

  useEffect(() => {
    localStorage.setItem('salon_billing_transactions', JSON.stringify(transactions));
  }, [transactions]);

  // Helper functions
  const generateInvoiceNumber = () => {
    const year = new Date().getFullYear();
    const count = invoices.length + 1;
    return `INV-${year}-${count.toString().padStart(3, '0')}`;
  };

  const calculateInvoiceTotal = (services, discountType, discountValue, taxRate) => {
    const subtotal = services.reduce((sum, service) => sum + service.price, 0);
    
    let discountAmount = 0;
    if (discountType === 'percentage') {
      discountAmount = (subtotal * discountValue) / 100;
    } else if (discountType === 'fixed') {
      discountAmount = discountValue;
    }
    
    const afterDiscount = subtotal - discountAmount;
    const taxAmount = (afterDiscount * taxRate) / 100;
    const total = afterDiscount + taxAmount;
    
    return {
      subtotal: parseFloat(subtotal.toFixed(2)),
      discountAmount: parseFloat(discountAmount.toFixed(2)),
      taxAmount: parseFloat(taxAmount.toFixed(2)),
      total: parseFloat(total.toFixed(2))
    };
  };

  const validateDiscount = (code, subtotal) => {
    const discount = discounts.find(d => 
      d.code.toLowerCase() === code.toLowerCase() && 
      d.status === 'active'
    );
    
    if (!discount) {
      return { valid: false, error: 'Invalid discount code' };
    }
    
    const now = new Date();
    now.setHours(0, 0, 0, 0);
    const validFrom = new Date(discount.validFrom);
    const validTo = new Date(discount.validTo);
    validTo.setHours(23, 59, 59, 999);

    if (now < validFrom) {
      return {
        valid: false,
        error: `Discount code is not yet valid. Valid from ${validFrom.toLocaleDateString()}`
      };
    }

    if (now > validTo) {
      return {
        valid: false,
        error: `Discount code has expired. Valid until ${validTo.toLocaleDateString()}`
      };
    }
    
    if (subtotal < discount.minAmount) {
      return { valid: false, error: `Minimum amount required: $${discount.minAmount}` };
    }
    
    if (discount.usedCount >= discount.usageLimit) {
      return { valid: false, error: 'Discount code usage limit reached' };
    }
    
    return { valid: true, discount };
  };

  const getRevenueStats = (period = 'month') => {
    const now = new Date();
    const paidInvoices = invoices.filter(inv => inv.status === 'paid');
    
    let filteredInvoices = paidInvoices;
    
    if (period === 'day') {
      const today = now.toISOString().split('T')[0];
      filteredInvoices = paidInvoices.filter(inv => inv.date === today);
    } else if (period === 'week') {
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      filteredInvoices = paidInvoices.filter(inv => new Date(inv.date) >= weekAgo);
    } else if (period === 'month') {
      const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      filteredInvoices = paidInvoices.filter(inv => new Date(inv.date) >= monthAgo);
    }
    
    const totalRevenue = filteredInvoices.reduce((sum, inv) => sum + inv.total, 0);
    const totalInvoices = filteredInvoices.length;
    const averageInvoice = totalInvoices > 0 ? totalRevenue / totalInvoices : 0;
    
    return {
      totalRevenue: parseFloat(totalRevenue.toFixed(2)),
      totalInvoices,
      averageInvoice: parseFloat(averageInvoice.toFixed(2))
    };
  };

  const getMostBookedServices = () => {
    const serviceStats = {};
    
    invoices.forEach(invoice => {
      invoice.services.forEach(service => {
        if (!serviceStats[service.name]) {
          serviceStats[service.name] = {
            name: service.name,
            count: 0,
            revenue: 0
          };
        }
        serviceStats[service.name].count += 1;
        serviceStats[service.name].revenue += service.price;
      });
    });
    
    return Object.values(serviceStats)
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  };

  const getStaffPerformance = () => {
    const staffStats = {};
    
    invoices.forEach(invoice => {
      invoice.services.forEach(service => {
        if (!staffStats[service.stylist]) {
          staffStats[service.stylist] = {
            name: service.stylist,
            serviceCount: 0,
            revenue: 0,
            averageService: 0
          };
        }
        staffStats[service.stylist].serviceCount += 1;
        staffStats[service.stylist].revenue += service.price;
      });
    });
    
    Object.values(staffStats).forEach(staff => {
      staff.averageService = staff.serviceCount > 0 ? staff.revenue / staff.serviceCount : 0;
      staff.revenue = parseFloat(staff.revenue.toFixed(2));
      staff.averageService = parseFloat(staff.averageService.toFixed(2));
    });
    
    return Object.values(staffStats).sort((a, b) => b.revenue - a.revenue);
  };

  // CRUD operations for invoices
  const createInvoice = (invoiceData) => {
    const newInvoice = {
      ...invoiceData,
      id: generateInvoiceNumber(),
      date: new Date().toISOString().split('T')[0],
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      status: 'pending',
      paymentStatus: 'pending',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    const totals = calculateInvoiceTotal(
      newInvoice.services,
      newInvoice.discountType,
      newInvoice.discountValue,
      newInvoice.taxRate || 8.5
    );

    Object.assign(newInvoice, totals);

    setInvoices(prev => [...prev, newInvoice]);
    return newInvoice;
  };

  const updateInvoice = (id, updates) => {
    setInvoices(prev => prev.map(invoice =>
      invoice.id === id
        ? { ...invoice, ...updates, updatedAt: new Date().toISOString() }
        : invoice
    ));
  };

  const deleteInvoice = (id) => {
    setInvoices(prev => prev.filter(invoice => invoice.id !== id));
    setPayments(prev => prev.filter(payment => payment.invoiceId !== id));
    setTransactions(prev => prev.filter(transaction => transaction.invoiceId !== id));
  };

  // Payment operations
  const processPayment = (paymentData) => {
    const newPayment = {
      ...paymentData,
      id: `PAY-${Date.now()}`,
      date: new Date().toISOString(),
      status: 'completed' // In real app, this would be set by payment gateway
    };

    setPayments(prev => [...prev, newPayment]);

    // Update invoice status
    updateInvoice(paymentData.invoiceId, {
      status: 'paid',
      paymentStatus: 'completed',
      paymentDate: newPayment.date,
      paymentMethod: paymentData.method,
      transactionId: newPayment.transactionId
    });

    // Add transaction record
    const transaction = {
      id: `TXN-${Date.now()}`,
      invoiceId: paymentData.invoiceId,
      type: 'payment',
      amount: paymentData.amount,
      method: paymentData.method,
      status: 'completed',
      date: newPayment.date,
      description: 'Payment for services'
    };

    setTransactions(prev => [...prev, transaction]);

    return newPayment;
  };

  // Discount operations
  const createDiscount = (discountData) => {
    const newDiscount = {
      ...discountData,
      id: Math.max(...discounts.map(d => d.id), 0) + 1,
      usedCount: 0,
      status: 'active'
    };

    setDiscounts(prev => [...prev, newDiscount]);
    return newDiscount;
  };

  const updateDiscount = (id, updates) => {
    setDiscounts(prev => prev.map(discount =>
      discount.id === id ? { ...discount, ...updates } : discount
    ));
  };

  const deleteDiscount = (id) => {
    setDiscounts(prev => prev.filter(discount => discount.id !== id));
  };

  const removeExpiredDiscounts = () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    setDiscounts(prev => prev.filter(discount => {
      const validToDate = new Date(discount.validTo);
      validToDate.setHours(23, 59, 59, 999);
      return validToDate >= today; // Keep only non-expired discounts
    }));
  };

  const getActiveDiscounts = () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    return discounts.filter(discount => {
      const validToDate = new Date(discount.validTo);
      validToDate.setHours(23, 59, 59, 999);
      return discount.status === 'active' && validToDate >= today;
    });
  };

  const applyDiscount = (code) => {
    setDiscounts(prev => prev.map(discount =>
      discount.code.toLowerCase() === code.toLowerCase()
        ? { ...discount, usedCount: discount.usedCount + 1 }
        : discount
    ));
  };

  const value = {
    // State
    invoices,
    payments,
    discounts,
    transactions,

    // Helper functions
    generateInvoiceNumber,
    calculateInvoiceTotal,
    validateDiscount,
    getRevenueStats,
    getMostBookedServices,
    getStaffPerformance,

    // CRUD operations
    createInvoice,
    updateInvoice,
    deleteInvoice,
    processPayment,
    createDiscount,
    updateDiscount,
    deleteDiscount,
    applyDiscount,
    removeExpiredDiscounts,
    getActiveDiscounts,

    // Setters for direct manipulation if needed
    setInvoices,
    setPayments,
    setDiscounts,
    setTransactions
  };

  return (
    <BillingContext.Provider value={value}>
      {children}
    </BillingContext.Provider>
  );
};
