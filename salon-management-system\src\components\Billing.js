import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tabs,
  Tab,
  Badge,
  Alert
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  GetApp as DownloadIcon,
  Payment as PaymentIcon,
  Receipt as ReceiptIcon,
  TrendingUp as TrendingUpIcon,
  AttachMoney as MoneyIcon,
  Assignment as InvoiceIcon,
  LocalOffer as DiscountIcon,
  Print as PrintIcon
} from '@mui/icons-material';
import { useBilling } from '../contexts/BillingContext';
import DiscountForm from './DiscountForm';
import PaymentProcessor from './PaymentProcessor';
import InvoiceForm from './InvoiceForm';
import { generateInvoicePDF, printInvoice, exportInvoicesPDF } from '../utils/pdfGenerator';

const Billing = () => {
  const {
    invoices,
    payments,
    discounts,
    getRevenueStats,
    deleteInvoice,
    processPayment,
    deleteDiscount,
    removeExpiredDiscounts,
    getActiveDiscounts
  } = useBilling();

  const [currentTab, setCurrentTab] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('all');
  const [amountFilter, setAmountFilter] = useState('all');
  const [sortBy, setSortBy] = useState('date');
  const [sortOrder, setSortOrder] = useState('desc');
  const [selectedInvoice, setSelectedInvoice] = useState(null);
  const [invoiceDialogOpen, setInvoiceDialogOpen] = useState(false);
  const [paymentDialogOpen, setPaymentDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [invoiceToDelete, setInvoiceToDelete] = useState(null);
  const [discountFormOpen, setDiscountFormOpen] = useState(false);
  const [editingDiscount, setEditingDiscount] = useState(null);
  const [discountFormMode, setDiscountFormMode] = useState('add');
  const [invoiceFormOpen, setInvoiceFormOpen] = useState(false);
  const [editingInvoice, setEditingInvoice] = useState(null);
  const [invoiceFormMode, setInvoiceFormMode] = useState('add');

  // Get filtered invoices
  const filteredInvoices = invoices.filter(invoice => {
    // Search functionality - search in multiple fields
    const matchesSearch = searchTerm === '' ||
      invoice.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.customerEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.customerPhone.includes(searchTerm) ||
      invoice.services.some(service =>
        service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        service.stylist.toLowerCase().includes(searchTerm.toLowerCase())
      );

    // Status filter
    const matchesStatus = statusFilter === 'all' || invoice.status === statusFilter;

    // Date filter
    const invoiceDate = new Date(invoice.date);
    const today = new Date();
    const matchesDate = dateFilter === 'all' ||
      (dateFilter === 'today' && invoiceDate.toDateString() === today.toDateString()) ||
      (dateFilter === 'week' && (today - invoiceDate) <= 7 * 24 * 60 * 60 * 1000) ||
      (dateFilter === 'month' && invoiceDate.getMonth() === today.getMonth() && invoiceDate.getFullYear() === today.getFullYear()) ||
      (dateFilter === 'year' && invoiceDate.getFullYear() === today.getFullYear());

    // Amount filter
    const matchesAmount = amountFilter === 'all' ||
      (amountFilter === 'low' && invoice.total < 50) ||
      (amountFilter === 'medium' && invoice.total >= 50 && invoice.total < 200) ||
      (amountFilter === 'high' && invoice.total >= 200);

    return matchesSearch && matchesStatus && matchesDate && matchesAmount;
  }).sort((a, b) => {
    let aValue, bValue;

    switch (sortBy) {
      case 'customer':
        aValue = a.customerName.toLowerCase();
        bValue = b.customerName.toLowerCase();
        break;
      case 'amount':
        aValue = a.total;
        bValue = b.total;
        break;
      case 'status':
        aValue = a.status;
        bValue = b.status;
        break;
      case 'date':
      default:
        aValue = new Date(a.date);
        bValue = new Date(b.date);
        break;
    }

    if (sortOrder === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  // Calculate statistics
  const revenueStats = getRevenueStats('month');
  const totalPending = invoices.filter(inv => inv.status === 'pending').reduce((sum, inv) => sum + inv.total, 0);
  const totalOverdue = invoices.filter(inv => {
    const dueDate = new Date(inv.dueDate);
    const today = new Date();
    return inv.status === 'pending' && dueDate < today;
  }).reduce((sum, inv) => sum + inv.total, 0);

  const handleViewInvoice = (invoice) => {
    setSelectedInvoice(invoice);
    setInvoiceDialogOpen(true);
  };

  const handlePaymentClick = (invoice) => {
    setSelectedInvoice(invoice);
    setPaymentDialogOpen(true);
  };

  const handleDeleteInvoice = (invoice) => {
    setInvoiceToDelete(invoice);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    if (invoiceToDelete) {
      deleteInvoice(invoiceToDelete.id);
      setDeleteDialogOpen(false);
      setInvoiceToDelete(null);
    }
  };

  const handleProcessPayment = () => {
    if (selectedInvoice) {
      setPaymentDialogOpen(false);
      setSelectedInvoice(null);
    }
  };

  const handleAddDiscount = () => {
    setEditingDiscount(null);
    setDiscountFormMode('add');
    setDiscountFormOpen(true);
  };

  const handleEditDiscount = (discount) => {
    setEditingDiscount(discount);
    setDiscountFormMode('edit');
    setDiscountFormOpen(true);
  };

  const handleDeleteDiscount = (discount) => {
    if (window.confirm(`Are you sure you want to delete discount "${discount.code}"?`)) {
      deleteDiscount(discount.id);
    }
  };

  const handleCloseDiscountForm = () => {
    setDiscountFormOpen(false);
    setEditingDiscount(null);
  };

  const handleCleanupExpiredDiscounts = () => {
    const expiredCount = discounts.filter(discount => {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const validToDate = new Date(discount.validTo);
      validToDate.setHours(23, 59, 59, 999);
      return validToDate < today;
    }).length;

    if (expiredCount === 0) {
      alert('No expired discounts found.');
      return;
    }

    if (window.confirm(`Remove ${expiredCount} expired discount(s) from the list?`)) {
      removeExpiredDiscounts();
    }
  };

  const isDiscountExpired = (discount) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const validToDate = new Date(discount.validTo);
    validToDate.setHours(23, 59, 59, 999);
    return validToDate < today;
  };

  const isDiscountExpiringSoon = (discount) => {
    const today = new Date();
    const validToDate = new Date(discount.validTo);
    const daysUntilExpiry = Math.ceil((validToDate - today) / (1000 * 60 * 60 * 24));
    return daysUntilExpiry <= 7 && daysUntilExpiry > 0;
  };

  const handleAddInvoice = () => {
    setEditingInvoice(null);
    setInvoiceFormMode('add');
    setInvoiceFormOpen(true);
  };

  const handleEditInvoice = (invoice) => {
    setEditingInvoice(invoice);
    setInvoiceFormMode('edit');
    setInvoiceFormOpen(true);
  };

  const handleCloseInvoiceForm = () => {
    setInvoiceFormOpen(false);
    setEditingInvoice(null);
  };

  const handleDownloadInvoice = (invoice) => {
    try {
      generateInvoicePDF(invoice);
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Error generating PDF. Please try again.');
    }
  };

  const handlePrintInvoice = (invoice) => {
    try {
      printInvoice(invoice);
    } catch (error) {
      console.error('Error printing invoice:', error);
      alert('Error printing invoice. Please try again.');
    }
  };

  const handleExportData = () => {
    try {
      exportInvoicesPDF(filteredInvoices);
    } catch (error) {
      console.error('Error exporting invoices:', error);
      alert('Error exporting invoices. Please try again.');
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'paid': return 'success';
      case 'pending': return 'warning';
      case 'overdue': return 'error';
      case 'cancelled': return 'default';
      default: return 'default';
    }
  };

  const TabPanel = ({ children, value, index }) => (
    <div hidden={value !== index}>
      {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
    </div>
  );

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold' }}>
          Billing & Payments
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<DownloadIcon />}
            onClick={handleExportData}
          >
            Export PDF
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            sx={{ bgcolor: 'primary.main' }}
            onClick={handleAddInvoice}
          >
            New Invoice
          </Button>
        </Box>
      </Box>

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Monthly Revenue
                  </Typography>
                  <Typography variant="h4" color="success.main">
                    {formatCurrency(revenueStats.totalRevenue)}
                  </Typography>
                </Box>
                <TrendingUpIcon sx={{ fontSize: 40, color: 'success.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Pending Payments
                  </Typography>
                  <Typography variant="h4" color="warning.main">
                    {formatCurrency(totalPending)}
                  </Typography>
                </Box>
                <PaymentIcon sx={{ fontSize: 40, color: 'warning.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Overdue Amount
                  </Typography>
                  <Typography variant="h4" color="error.main">
                    {formatCurrency(totalOverdue)}
                  </Typography>
                </Box>
                <Badge badgeContent={invoices.filter(inv => {
                  const dueDate = new Date(inv.dueDate);
                  const today = new Date();
                  return inv.status === 'pending' && dueDate < today;
                }).length} color="error">
                  <MoneyIcon sx={{ fontSize: 40, color: 'error.main' }} />
                </Badge>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography color="textSecondary" gutterBottom>
                    Total Invoices
                  </Typography>
                  <Typography variant="h4" color="primary.main">
                    {invoices.length}
                  </Typography>
                </Box>
                <InvoiceIcon sx={{ fontSize: 40, color: 'primary.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={currentTab} onChange={(e, newValue) => setCurrentTab(newValue)}>
          <Tab label="Invoices" />
          <Tab label="Payments" />
          <Tab label="Discounts" />
        </Tabs>
      </Paper>

      {/* Invoices Tab */}
      <TabPanel value={currentTab} index={0}>
        {/* Search and Filters */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Search by customer, invoice ID, email, phone, service, or stylist..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  label="Status"
                >
                  <MenuItem value="all">All Status</MenuItem>
                  <MenuItem value="pending">Pending</MenuItem>
                  <MenuItem value="paid">Paid</MenuItem>
                  <MenuItem value="overdue">Overdue</MenuItem>
                  <MenuItem value="cancelled">Cancelled</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Date</InputLabel>
                <Select
                  value={dateFilter}
                  onChange={(e) => setDateFilter(e.target.value)}
                  label="Date"
                >
                  <MenuItem value="all">All Dates</MenuItem>
                  <MenuItem value="today">Today</MenuItem>
                  <MenuItem value="week">This Week</MenuItem>
                  <MenuItem value="month">This Month</MenuItem>
                  <MenuItem value="year">This Year</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Amount</InputLabel>
                <Select
                  value={amountFilter}
                  onChange={(e) => setAmountFilter(e.target.value)}
                  label="Amount"
                >
                  <MenuItem value="all">All Amounts</MenuItem>
                  <MenuItem value="low">Under $50</MenuItem>
                  <MenuItem value="medium">$50 - $200</MenuItem>
                  <MenuItem value="high">Over $200</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth>
                <InputLabel>Sort By</InputLabel>
                <Select
                  value={`${sortBy}-${sortOrder}`}
                  onChange={(e) => {
                    const [field, order] = e.target.value.split('-');
                    setSortBy(field);
                    setSortOrder(order);
                  }}
                  label="Sort By"
                >
                  <MenuItem value="date-desc">Date (Newest)</MenuItem>
                  <MenuItem value="date-asc">Date (Oldest)</MenuItem>
                  <MenuItem value="customer-asc">Customer (A-Z)</MenuItem>
                  <MenuItem value="customer-desc">Customer (Z-A)</MenuItem>
                  <MenuItem value="amount-desc">Amount (High-Low)</MenuItem>
                  <MenuItem value="amount-asc">Amount (Low-High)</MenuItem>
                  <MenuItem value="status-asc">Status (A-Z)</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>

          {/* Filter Summary */}
          {(searchTerm || statusFilter !== 'all' || dateFilter !== 'all' || amountFilter !== 'all') && (
            <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
              <Typography variant="subtitle2" gutterBottom>
                Active Filters:
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', alignItems: 'center' }}>
                {searchTerm && (
                  <Chip
                    label={`Search: "${searchTerm}"`}
                    onDelete={() => setSearchTerm('')}
                    size="small"
                  />
                )}
                {statusFilter !== 'all' && (
                  <Chip
                    label={`Status: ${statusFilter}`}
                    onDelete={() => setStatusFilter('all')}
                    size="small"
                  />
                )}
                {dateFilter !== 'all' && (
                  <Chip
                    label={`Date: ${dateFilter}`}
                    onDelete={() => setDateFilter('all')}
                    size="small"
                  />
                )}
                {amountFilter !== 'all' && (
                  <Chip
                    label={`Amount: ${amountFilter}`}
                    onDelete={() => setAmountFilter('all')}
                    size="small"
                  />
                )}
                <Button
                  size="small"
                  onClick={() => {
                    setSearchTerm('');
                    setStatusFilter('all');
                    setDateFilter('all');
                    setAmountFilter('all');
                  }}
                >
                  Clear All
                </Button>
              </Box>
              <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
                Showing {filteredInvoices.length} of {invoices.length} invoices
              </Typography>
            </Box>
          )}
        </Paper>

        {/* Invoices Table */}
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Invoice #</TableCell>
                <TableCell>Customer</TableCell>
                <TableCell>Date</TableCell>
                <TableCell>Due Date</TableCell>
                <TableCell>Amount</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredInvoices.map((invoice) => {
                const isOverdue = new Date(invoice.dueDate) < new Date() && invoice.status === 'pending';
                
                return (
                  <TableRow key={invoice.id}>
                    <TableCell>
                      <Typography variant="subtitle2" fontWeight="bold">
                        {invoice.id}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="subtitle2">
                          {invoice.customerName}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          {invoice.customerEmail}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>{invoice.date}</TableCell>
                    <TableCell>
                      <Typography color={isOverdue ? 'error.main' : 'inherit'}>
                        {invoice.dueDate}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="subtitle2" fontWeight="bold">
                        {formatCurrency(invoice.total)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={isOverdue ? 'Overdue' : invoice.status} 
                        color={isOverdue ? 'error' : getStatusColor(invoice.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Tooltip title="View Invoice">
                          <IconButton
                            size="small"
                            color="primary"
                            onClick={() => handleViewInvoice(invoice)}
                          >
                            <ViewIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Edit Invoice">
                          <IconButton
                            size="small"
                            color="primary"
                            onClick={() => handleEditInvoice(invoice)}
                          >
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                        {invoice.status === 'pending' && (
                          <Tooltip title="Process Payment">
                            <IconButton
                              size="small"
                              color="success"
                              onClick={() => handlePaymentClick(invoice)}
                            >
                              <PaymentIcon />
                            </IconButton>
                          </Tooltip>
                        )}
                        <Tooltip title="Download PDF">
                          <IconButton
                            size="small"
                            color="info"
                            onClick={() => handleDownloadInvoice(invoice)}
                          >
                            <DownloadIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Delete Invoice">
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => handleDeleteInvoice(invoice)}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      {/* Payments Tab */}
      <TabPanel value={currentTab} index={1}>
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Payment ID</TableCell>
                <TableCell>Invoice</TableCell>
                <TableCell>Amount</TableCell>
                <TableCell>Method</TableCell>
                <TableCell>Date</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Transaction ID</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {payments.map((payment) => (
                <TableRow key={payment.id}>
                  <TableCell>
                    <Typography variant="subtitle2" fontWeight="bold">
                      {payment.id}
                    </Typography>
                  </TableCell>
                  <TableCell>{payment.invoiceId}</TableCell>
                  <TableCell>
                    <Typography variant="subtitle2" fontWeight="bold">
                      {formatCurrency(payment.amount)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={payment.method.toUpperCase()}
                      size="small"
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>{new Date(payment.date).toLocaleDateString()}</TableCell>
                  <TableCell>
                    <Chip
                      label={payment.status}
                      color={payment.status === 'completed' ? 'success' : 'warning'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="textSecondary">
                      {payment.transactionId}
                    </Typography>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      {/* Discounts Tab */}
      <TabPanel value={currentTab} index={2}>
        <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Button
            variant="outlined"
            color="warning"
            startIcon={<DeleteIcon />}
            onClick={handleCleanupExpiredDiscounts}
          >
            Remove Expired
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            sx={{ bgcolor: 'primary.main' }}
            onClick={handleAddDiscount}
          >
            Add Discount
          </Button>
        </Box>

        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Code</TableCell>
                <TableCell>Name</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Value</TableCell>
                <TableCell>Usage</TableCell>
                <TableCell>Valid Period</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {discounts.map((discount) => (
                <TableRow key={discount.id}>
                  <TableCell>
                    <Typography variant="subtitle2" fontWeight="bold">
                      {discount.code}
                    </Typography>
                  </TableCell>
                  <TableCell>{discount.name}</TableCell>
                  <TableCell>
                    <Chip
                      label={discount.type === 'percentage' ? 'Percentage' : 'Fixed Amount'}
                      size="small"
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="subtitle2">
                      {discount.type === 'percentage' ? `${discount.value}%` : formatCurrency(discount.value)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {discount.usedCount} / {discount.usageLimit}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Box>
                      <Typography variant="body2">
                        {new Date(discount.validFrom).toLocaleDateString()} to {new Date(discount.validTo).toLocaleDateString()}
                      </Typography>
                      {isDiscountExpired(discount) && (
                        <Typography variant="caption" color="error">
                          Expired
                        </Typography>
                      )}
                      {isDiscountExpiringSoon(discount) && !isDiscountExpired(discount) && (
                        <Typography variant="caption" color="warning.main">
                          Expires soon
                        </Typography>
                      )}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={
                        isDiscountExpired(discount)
                          ? 'Expired'
                          : isDiscountExpiringSoon(discount)
                            ? 'Expiring Soon'
                            : discount.status
                      }
                      color={
                        isDiscountExpired(discount)
                          ? 'error'
                          : isDiscountExpiringSoon(discount)
                            ? 'warning'
                            : discount.status === 'active'
                              ? 'success'
                              : 'default'
                      }
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Tooltip title="Edit Discount">
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={() => handleEditDiscount(discount)}
                        >
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete Discount">
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleDeleteDiscount(discount)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      {/* Invoice View Dialog */}
      <Dialog
        open={invoiceDialogOpen}
        onClose={() => setInvoiceDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            Invoice Details
            <Box>
              <IconButton
                color="primary"
                onClick={() => selectedInvoice && handlePrintInvoice(selectedInvoice)}
                title="Print Invoice"
              >
                <PrintIcon />
              </IconButton>
              <IconButton
                color="primary"
                onClick={() => selectedInvoice && handleDownloadInvoice(selectedInvoice)}
                title="Download PDF"
              >
                <DownloadIcon />
              </IconButton>
            </Box>
          </Box>
        </DialogTitle>
        <DialogContent>
          {selectedInvoice && (
            <Box sx={{ p: 2 }}>
              {/* Invoice Header */}
              <Grid container spacing={3} sx={{ mb: 3 }}>
                <Grid item xs={6}>
                  <Typography variant="h6" gutterBottom>
                    Salon Management System
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    123 Beauty Street<br />
                    City, State 12345<br />
                    Phone: (*************
                  </Typography>
                </Grid>
                <Grid item xs={6} sx={{ textAlign: 'right' }}>
                  <Typography variant="h5" fontWeight="bold">
                    INVOICE
                  </Typography>
                  <Typography variant="h6" color="primary.main">
                    {selectedInvoice.id}
                  </Typography>
                  <Typography variant="body2">
                    Date: {selectedInvoice.date}<br />
                    Due: {selectedInvoice.dueDate}
                  </Typography>
                </Grid>
              </Grid>

              {/* Customer Info */}
              <Box sx={{ mb: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                <Typography variant="h6" gutterBottom>
                  Bill To:
                </Typography>
                <Typography variant="body1" fontWeight="bold">
                  {selectedInvoice.customerName}
                </Typography>
                <Typography variant="body2">
                  {selectedInvoice.customerEmail}<br />
                  {selectedInvoice.customerPhone}
                </Typography>
              </Box>

              {/* Services Table */}
              <TableContainer sx={{ mb: 3 }}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Service</TableCell>
                      <TableCell>Stylist</TableCell>
                      <TableCell align="right">Qty</TableCell>
                      <TableCell align="right">Price</TableCell>
                      <TableCell align="right">Total</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {selectedInvoice.services.map((service, index) => (
                      <TableRow key={index}>
                        <TableCell>{service.name}</TableCell>
                        <TableCell>{service.stylist}</TableCell>
                        <TableCell align="right">{service.quantity}</TableCell>
                        <TableCell align="right">{formatCurrency(service.price)}</TableCell>
                        <TableCell align="right">
                          {formatCurrency(service.price * service.quantity)}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* Totals */}
              <Box sx={{ ml: 'auto', width: 300 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>Subtotal:</Typography>
                  <Typography>{formatCurrency(selectedInvoice.subtotal)}</Typography>
                </Box>
                {selectedInvoice.discountAmount > 0 && (
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography color="success.main">
                      Discount ({selectedInvoice.discountType === 'percentage' ? `${selectedInvoice.discountValue}%` : 'Fixed'}):
                    </Typography>
                    <Typography color="success.main">
                      -{formatCurrency(selectedInvoice.discountAmount)}
                    </Typography>
                  </Box>
                )}
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>Tax ({selectedInvoice.taxRate}%):</Typography>
                  <Typography>{formatCurrency(selectedInvoice.taxAmount)}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', borderTop: 1, pt: 1 }}>
                  <Typography variant="h6" fontWeight="bold">Total:</Typography>
                  <Typography variant="h6" fontWeight="bold">
                    {formatCurrency(selectedInvoice.total)}
                  </Typography>
                </Box>
              </Box>

              {selectedInvoice.notes && (
                <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Notes:
                  </Typography>
                  <Typography variant="body2">
                    {selectedInvoice.notes}
                  </Typography>
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setInvoiceDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Payment Processor */}
      <PaymentProcessor
        open={paymentDialogOpen}
        onClose={() => setPaymentDialogOpen(false)}
        invoice={selectedInvoice}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete invoice "{invoiceToDelete?.id}"? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={confirmDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Discount Form Dialog */}
      <DiscountForm
        open={discountFormOpen}
        onClose={handleCloseDiscountForm}
        discount={editingDiscount}
        mode={discountFormMode}
      />

      {/* Invoice Form Dialog */}
      <InvoiceForm
        open={invoiceFormOpen}
        onClose={handleCloseInvoiceForm}
        invoice={editingInvoice}
        mode={invoiceFormMode}
      />
    </Box>
  );
};

export default Billing;
