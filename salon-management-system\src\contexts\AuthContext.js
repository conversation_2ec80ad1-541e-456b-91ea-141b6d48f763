import React, { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Mock user data - In a real app, this would come from a backend
const mockUsers = {
  admin: {
    id: 1,
    username: 'admin',
    password: 'admin123',
    role: 'admin',
    name: 'System Administrator',
    email: '<EMAIL>',
    permissions: ['all']
  },
  staff: [
    {
      id: 2,
      username: 'stylist1',
      password: 'staff123',
      role: 'staff',
      name: '<PERSON>',
      email: '<EMAIL>',
      position: 'Senior Stylist',
      permissions: ['appointments', 'customers', 'services']
    },
    {
      id: 3,
      username: 'receptionist1',
      password: 'staff123',
      role: 'staff',
      name: '<PERSON>',
      email: '<EMAIL>',
      position: 'Receptionist',
      permissions: ['appointments', 'customers']
    }
  ]
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check if user is logged in from localStorage
    const savedUser = localStorage.getItem('salonUser');
    if (savedUser) {
      setUser(JSON.parse(savedUser));
    }
    setLoading(false);
  }, []);

  const login = async (username, password, userType = 'staff') => {
    try {
      let foundUser = null;

      // Trim whitespace from inputs
      const trimmedUsername = username.trim();
      const trimmedPassword = password.trim();

      // Check admin
      if (userType === 'admin' && mockUsers.admin.username === trimmedUsername && mockUsers.admin.password === trimmedPassword) {
        foundUser = mockUsers.admin;
      }
      // Check staff
      else if (userType === 'staff') {
        foundUser = mockUsers.staff.find(staff =>
          staff.username === trimmedUsername && staff.password === trimmedPassword
        );
      }

      if (foundUser) {
        const userWithoutPassword = { ...foundUser };
        delete userWithoutPassword.password;

        setUser(userWithoutPassword);
        localStorage.setItem('salonUser', JSON.stringify(userWithoutPassword));
        return { success: true, user: userWithoutPassword };
      } else {
        return { success: false, error: 'Invalid credentials' };
      }
    } catch (error) {
      return { success: false, error: 'Login failed' };
    }
  };



  const logout = () => {
    setUser(null);
    localStorage.removeItem('salonUser');
  };

  const hasPermission = (permission) => {
    if (!user) return false;
    if (user.role === 'admin') return true;
    return user.permissions && user.permissions.includes(permission);
  };

  const isAdmin = () => user && user.role === 'admin';
  const isStaff = () => user && user.role === 'staff';

  const value = {
    user,
    login,
    logout,
    hasPermission,
    isAdmin,
    isStaff,
    loading
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
