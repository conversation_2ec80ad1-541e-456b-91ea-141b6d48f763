import React from 'react';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  LinearProgress,
  Alert,
  Button,
} from '@mui/material';
import {
  TrendingUp,
  People,
  Event,
  AttachMoney,
  Schedule,
  Person,
  CalendarToday,
  PersonAdd,
  Visibility,
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { useInventory } from '../contexts/InventoryContext';
import InventoryAlerts from './InventoryAlerts';

const Dashboard = () => {
  const { user, isAdmin, isStaff } = useAuth();
  const navigate = useNavigate();
  const {
    products,
    getLowStockProducts,
    getOutOfStockProducts,
    getTotalInventoryValue
  } = useInventory();

  // Role-based stats - different data based on user role
  const getStatsForRole = () => {
    const lowStockCount = getLowStockProducts().length;
    const outOfStockCount = getOutOfStockProducts().length;
    const totalInventoryValue = getTotalInventoryValue();

    if (isAdmin()) {
      return [
        {
          title: 'Today\'s Revenue',
          value: '$1,250',
          icon: <AttachMoney />,
          color: '#4caf50',
          change: '+12%',
        },
        {
          title: 'Appointments Today',
          value: '24',
          icon: <Event />,
          color: '#2196f3',
          change: '+5%',
        },
        {
          title: 'Total Customers',
          value: '1,847',
          icon: <People />,
          color: '#ff9800',
          change: '+8%',
        },
        {
          title: 'Inventory Value',
          value: `$${totalInventoryValue.toLocaleString()}`,
          icon: <TrendingUp />,
          color: '#9c27b0',
          change: lowStockCount > 0 ? `${lowStockCount} low stock` : 'All good',
          alert: lowStockCount > 0 || outOfStockCount > 0
        },
      ];
    } else if (isStaff()) {
      return [
        {
          title: 'My Appointments Today',
          value: '8',
          icon: <Event />,
          color: '#2196f3',
          change: '+2',
        },
        {
          title: 'Completed Today',
          value: '3',
          icon: <Schedule />,
          color: '#4caf50',
          change: '+1',
        },
        {
          title: 'My Customers',
          value: '156',
          icon: <People />,
          color: '#ff9800',
          change: '+5',
        },
        {
          title: 'Today\'s Earnings',
          value: '$320',
          icon: <AttachMoney />,
          color: '#9c27b0',
          change: '+15%',
        },
      ];
    }
  };

  const stats = getStatsForRole();

  const todayAppointments = [
    {
      id: 1,
      customer: 'Sarah Johnson',
      service: 'Hair Cut & Style',
      time: '9:00 AM',
      status: 'completed',
      stylist: 'Emma Wilson',
    },
    {
      id: 2,
      customer: 'Mike Davis',
      service: 'Beard Trim',
      time: '10:30 AM',
      status: 'in-progress',
      stylist: 'John Smith',
    },
    {
      id: 3,
      customer: 'Lisa Brown',
      service: 'Hair Color',
      time: '11:00 AM',
      status: 'scheduled',
      stylist: 'Emma Wilson',
    },
    {
      id: 4,
      customer: 'Tom Wilson',
      service: 'Full Service',
      time: '2:00 PM',
      status: 'scheduled',
      stylist: 'Mike Johnson',
    },
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'in-progress':
        return 'warning';
      case 'scheduled':
        return 'primary';
      default:
        return 'default';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed':
        return 'Completed';
      case 'in-progress':
        return 'In Progress';
      case 'scheduled':
        return 'Scheduled';
      default:
        return status;
    }
  };

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          Dashboard
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {stats.map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card sx={{
              height: '100%',
              border: stat.alert ? '2px solid #ff9800' : 'none',
              boxShadow: stat.alert ? '0 4px 8px rgba(255, 152, 0, 0.2)' : undefined
            }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar sx={{ bgcolor: stat.color, mr: 2 }}>
                    {stat.icon}
                  </Avatar>
                  <Box>
                    <Typography variant="h4" component="div">
                      {stat.value}
                    </Typography>
                    <Typography color="text.secondary" variant="body2">
                      {stat.title}
                    </Typography>
                  </Box>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <TrendingUp sx={{
                    color: stat.alert ? '#ff9800' : '#4caf50',
                    mr: 1
                  }} />
                  <Typography
                    variant="body2"
                    color={stat.alert ? 'warning.main' : 'success.main'}
                  >
                    {stat.change} from yesterday
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Inventory Alerts - Only for Admin and Staff */}
      {(isAdmin() || isStaff()) && (
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Inventory Alerts
              </Typography>
              <InventoryAlerts showInDashboard={true} />
            </Paper>
          </Grid>
        </Grid>
      )}

      <Grid container spacing={3}>
        {/* Appointments Section - Different content based on role */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              {isStaff() ? 'My Today\'s Appointments' : 'Today\'s Appointments'}
            </Typography>
            <List>
                {todayAppointments.map((appointment) => (
                  <ListItem key={appointment.id} divider>
                    <ListItemAvatar>
                      <Avatar>
                        <Schedule />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="subtitle1">
                            {appointment.customer}
                          </Typography>
                          <Chip
                            label={getStatusText(appointment.status)}
                            color={getStatusColor(appointment.status)}
                            size="small"
                          />
                        </Box>
                      }
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            {appointment.service} • {appointment.time}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Stylist: {appointment.stylist}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
          </Paper>
        </Grid>

        {/* Right Sidebar - Role-based content */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              {isAdmin() ? "Today's Progress" : "My Progress"}
            </Typography>
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Appointments Completed
              </Typography>
              <LinearProgress
                variant="determinate"
                value={isStaff() ? 37 : 25}
                sx={{ mt: 1, mb: 1 }}
              />
              <Typography variant="body2">
                {isStaff() ? '3 of 8 completed' : '6 of 24 completed'}
              </Typography>
            </Box>
            {isAdmin() && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Revenue Target
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={62}
                  sx={{ mt: 1, mb: 1 }}
                  color="success"
                />
                <Typography variant="body2">
                  $1,250 of $2,000 target
                </Typography>
              </Box>
            )}
          </Paper>

          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Quick Actions
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Button
                variant="outlined"
                startIcon={<CalendarToday />}
                onClick={() => navigate('/appointments')}
                fullWidth
              >
                New Appointment
              </Button>
              {(isAdmin() || (isStaff() && user?.permissions?.includes('customers'))) && (
                <Button
                  variant="outlined"
                  startIcon={<PersonAdd />}
                  onClick={() => navigate('/customers')}
                  fullWidth
                >
                  Add Customer
                </Button>
              )}
              <Button
                variant="outlined"
                startIcon={<Visibility />}
                onClick={() => navigate('/appointments')}
                fullWidth
              >
                View Schedule
              </Button>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
